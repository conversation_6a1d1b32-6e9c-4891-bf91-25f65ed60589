// Employer Profile Management Functions

const prisma = require("../../config/prisma");
const { convertBigIntToNumber } = require("../../utils/bigintHelper");

// Fetch profiles by employer ID
exports.getProfilesByEmployerId = async (req, res) => {
  const { emp_id } = req.params;

  try {
    const profiles = await prisma.employerprofiles.findMany({
      where: { emp_id: Number(emp_id) },
    });

    if (!profiles || profiles.length === 0) {
      return res
        .status(404)
        .json({ message: "No profiles found for this employer." });
    }

    res.status(200).json({
      message: "Profiles fetched successfully.",
      data: convertBigIntToNumber(profiles),
    });
  } catch (error) {
    console.error("Error fetching profiles:", error);
    res.status(500).send("Failed to fetch profiles.");
  }
};

// Get employer profile plan and candidate counts
exports.getEmployerProfilesData = async (req, res) => {
  const emp_id = req.headers["authorization"];

  if (!emp_id) {
    return res
      .status(400)
      .json({ message: "Unauthorized Access, Please Log in Again." });
  }

  try {
    const employerPlan = await prisma.employerplans.findFirst({
      where: { emp_id: Number(emp_id) },
      select: { plan_id: true, creditsLeft: true, end_date: true },
    });

    if (!employerPlan) {
      return res.status(404).json({
        message: "No employer profile plan found. Please log in again.",
      });
    }

    const planDetails = await prisma.plans.findUnique({
      where: { id: employerPlan.plan_id },
    });

    if (!planDetails) {
      return res.status(404).json({
        message: "No plan details found for the employer profile.",
      });
    }

    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) },
      select: { emp_name: true, profile_picture: true },
    });

    if (!employer) {
      return res.status(404).json({
        message: "Employer not found. Please log in again.",
      });
    }

    const shortlisted_count = await prisma.employerprofiles.count({
      where: { emp_id: Number(emp_id), status: "shortlisted" },
    });

    const unlocked_count = await prisma.employerprofiles.count({
      where: { emp_id: Number(emp_id), status: "unlocked" },
    });

    res.status(200).json({
      message: "Employer profile plan fetched successfully.",
      data: convertBigIntToNumber({
        ...planDetails,
        emp_name: employer.emp_name,
        profile_picture: employer.profile_picture,
        creditsLeft: employerPlan.creditsLeft || 0,
        end_date: employerPlan.end_date || 0,
        candidate_counts: {
          shortlisted_count,
          unlocked_count,
        },
      }),
    });
  } catch (error) {
    console.error("Error fetching profiles:", error);
    return res.status(500).json({ message: "Failed to fetch profiles." });
  }
};

// Update employer profile
exports.updateEmployerProfile = async (req, res) => {
  const emp_id = req.headers.authorization;
  const { emp_name, company_website } = req.body;

  if (!emp_id) {
    return res.status(401).json({
      message: "Authorization header (employer ID) is required.",
    });
  }

  try {
    // Validate input
    if (!emp_name || !company_website) {
      return res.status(400).json({
        message: "Company name and website are required.",
      });
    }

    // Check if employer exists
    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) },
    });

    if (!employer) {
      return res.status(404).json({
        message: "Employer not found.",
      });
    }

    // Prepare update data for employer
    const employerUpdateData = {
      emp_name: emp_name.trim(),
    };

    // Update employer record
    await prisma.employer.update({
      where: { id: Number(emp_id) },
      data: employerUpdateData,
    });

    // Prepare update data for company
    const companyUpdateData = {
      company_website: company_website.trim(),
    };

    // Handle company logo upload if provided
    if (req.files?.company_logo) {
      const logoFile = req.files.company_logo[0];
      const logoPath = logoFile.path.replace(/\\/g, "/");
      companyUpdateData.company_logo = logoPath;
    }

    // Update company record if company_id exists
    if (employer.company_id) {
      await prisma.company.update({
        where: { id: employer.company_id },
        data: companyUpdateData,
      });
    } else {
      // If no company exists, create one
      const newCompany = await prisma.company.create({
        data: {
          company_name: emp_name.trim(),
          company_website: company_website.trim(),
          ...companyUpdateData,
        },
      });

      // Link the new company to the employer
      await prisma.employer.update({
        where: { id: Number(emp_id) },
        data: { company_id: newCompany.id },
      });
    }

    res.status(200).json({
      message: "Employer profile updated successfully.",
      data: {
        emp_name: emp_name.trim(),
        company_website: company_website.trim(),
        ...(req.files?.company_logo && { company_logo: req.files.company_logo[0].path.replace(/\\/g, "/") }),
      },
    });
  } catch (error) {
    console.error("Error updating employer profile:", error);
    return res.status(500).json({
      message: "Failed to update employer profile.",
      error: error.message
    });
  }
};

// Get employer and company details
exports.getEmployerDetails = async (req, res) => {
  const emp_id = req.headers["authorization"];

  if (!emp_id) {
    return res
      .status(400)
      .json({ message: "Unauthorized Access, Please Log in Again." });
  }

  try {
    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) },
    });

    if (!employer) {
      return res.status(404).json({
        message: "No employer details Found. Please log in again.",
      });
    }

    // Only fetch company if company_id is not null/undefined
    let company = null;
    if (employer.company_id !== null && employer.company_id !== undefined) {
      company = await prisma.company.findUnique({
        where: { id: employer.company_id },
      });
    }

    if (!company) {
      return res.status(404).json({
        message: "No plan details found for the employer profile.",
      });
    }

    res.status(200).json({
      message: "Employer profile data fetched successfully.",
      data: convertBigIntToNumber({
        ...employer,
        ...company,
      }),
    });
  } catch (error) {
    console.error("Error fetching Employer Data:", error);
    return res.status(500).json({ message: "Failed to fetch Employer Data." });
  }
};